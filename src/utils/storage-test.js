import storageManager, { STORAGE_TYPE } from './storage.js';

/**
 * 存储工具类测试
 */
class StorageTest {
  
  /**
   * 基础功能测试
   */
  async testBasicFunctions() {
    console.log('=== 基础功能测试 ===');
    
    try {
      // 测试localStorage
      await storageManager.setItem('test-string', 'Hello World');
      const stringData = await storageManager.getItem('test-string');
      console.log('字符串存储测试:', stringData === 'Hello World' ? '✅ 通过' : '❌ 失败');
      
      // 测试对象存储
      const testObj = { name: '张三', age: 25, hobbies: ['读书', '游泳'] };
      await storageManager.setItem('test-object', testObj);
      const objData = await storageManager.getItem('test-object');
      console.log('对象存储测试:', JSON.stringify(objData) === JSON.stringify(testObj) ? '✅ 通过' : '❌ 失败');
      
      // 测试数组存储
      const testArray = [1, 2, 3, 'test', { key: 'value' }];
      await storageManager.setItem('test-array', testArray);
      const arrayData = await storageManager.getItem('test-array');
      console.log('数组存储测试:', JSON.stringify(arrayData) === JSON.stringify(testArray) ? '✅ 通过' : '❌ 失败');
      
    } catch (error) {
      console.error('基础功能测试失败:', error);
    }
  }
  
  /**
   * 不同存储类型测试
   */
  async testStorageTypes() {
    console.log('=== 存储类型测试 ===');
    
    const testData = { message: '测试数据', timestamp: Date.now() };
    
    try {
      // localStorage测试
      await storageManager.setItem('type-test', testData, { type: STORAGE_TYPE.LOCALSTORAGE });
      const localData = await storageManager.getItem('type-test', { type: STORAGE_TYPE.LOCALSTORAGE });
      console.log('localStorage测试:', localData ? '✅ 通过' : '❌ 失败');
      
      // sessionStorage测试
      await storageManager.setItem('type-test', testData, { type: STORAGE_TYPE.SESSION });
      const sessionData = await storageManager.getItem('type-test', { type: STORAGE_TYPE.SESSION });
      console.log('sessionStorage测试:', sessionData ? '✅ 通过' : '❌ 失败');
      
      // Cookie测试
      await storageManager.setItem('type-test', testData, { type: STORAGE_TYPE.COOKIE });
      const cookieData = await storageManager.getItem('type-test', { type: STORAGE_TYPE.COOKIE });
      console.log('Cookie测试:', cookieData ? '✅ 通过' : '❌ 失败');
      
      // localForage测试
      await storageManager.setItem('type-test', testData, { type: STORAGE_TYPE.LOCALFORAGE });
      const forageData = await storageManager.getItem('type-test', { type: STORAGE_TYPE.LOCALFORAGE });
      console.log('localForage测试:', forageData ? '✅ 通过' : '❌ 失败');
      
    } catch (error) {
      console.error('存储类型测试失败:', error);
    }
  }
  
  /**
   * 过期时间测试
   */
  async testExpiration() {
    console.log('=== 过期时间测试 ===');
    
    try {
      // 设置2秒后过期的数据
      await storageManager.setItem('expire-test', '即将过期的数据', { expire: 2000 });
      
      // 立即获取
      const immediateData = await storageManager.getItem('expire-test');
      console.log('立即获取:', immediateData ? '✅ 通过' : '❌ 失败');
      
      // 3秒后获取（应该已过期）
      setTimeout(async () => {
        const expiredData = await storageManager.getItem('expire-test');
        console.log('过期后获取:', expiredData === null ? '✅ 通过' : '❌ 失败');
      }, 3000);
      
    } catch (error) {
      console.error('过期时间测试失败:', error);
    }
  }
  
  /**
   * 加密功能测试
   */
  async testEncryption() {
    console.log('=== 加密功能测试 ===');
    
    try {
      const sensitiveData = { password: '123456', token: 'secret-token' };
      
      // 加密存储
      await storageManager.setItem('encrypted-test', sensitiveData, { encrypt: true });
      
      // 解密获取
      const decryptedData = await storageManager.getItem('encrypted-test', { encrypt: true });
      console.log('加密存储测试:', JSON.stringify(decryptedData) === JSON.stringify(sensitiveData) ? '✅ 通过' : '❌ 失败');
      
      // 检查原始存储数据是否已加密
      const rawData = localStorage.getItem('encrypted-test');
      const isEncrypted = rawData !== JSON.stringify(sensitiveData);
      console.log('数据加密验证:', isEncrypted ? '✅ 通过' : '❌ 失败');
      
    } catch (error) {
      console.error('加密功能测试失败:', error);
    }
  }
  
  /**
   * 工具方法测试
   */
  async testUtilityMethods() {
    console.log('=== 工具方法测试 ===');
    
    try {
      // 存储测试数据
      await storageManager.setItem('util-test-1', 'data1');
      await storageManager.setItem('util-test-2', 'data2');
      
      // hasItem测试
      const hasItem1 = await storageManager.hasItem('util-test-1');
      const hasItem3 = await storageManager.hasItem('util-test-3');
      console.log('hasItem测试:', hasItem1 && !hasItem3 ? '✅ 通过' : '❌ 失败');
      
      // getKeys测试
      const keys = await storageManager.getKeys(STORAGE_TYPE.LOCALSTORAGE);
      const hasTestKeys = keys.includes('util-test-1') && keys.includes('util-test-2');
      console.log('getKeys测试:', hasTestKeys ? '✅ 通过' : '❌ 失败');
      
      // removeItem测试
      await storageManager.removeItem('util-test-1');
      const removedItem = await storageManager.getItem('util-test-1');
      console.log('removeItem测试:', removedItem === null ? '✅ 通过' : '❌ 失败');
      
    } catch (error) {
      console.error('工具方法测试失败:', error);
    }
  }
  
  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始存储工具类测试...\n');
    
    await this.testBasicFunctions();
    console.log('');
    
    await this.testStorageTypes();
    console.log('');
    
    await this.testExpiration();
    console.log('');
    
    await this.testEncryption();
    console.log('');
    
    await this.testUtilityMethods();
    console.log('');
    
    console.log('✨ 测试完成！');
  }
  
  /**
   * 清理测试数据
   */
  async cleanup() {
    console.log('🧹 清理测试数据...');
    
    const testKeys = [
      'test-string', 'test-object', 'test-array',
      'type-test', 'expire-test', 'encrypted-test',
      'util-test-1', 'util-test-2'
    ];
    
    for (const key of testKeys) {
      try {
        await storageManager.removeItem(key, { type: STORAGE_TYPE.LOCALSTORAGE });
        await storageManager.removeItem(key, { type: STORAGE_TYPE.SESSION });
        await storageManager.removeItem(key, { type: STORAGE_TYPE.COOKIE });
        await storageManager.removeItem(key, { type: STORAGE_TYPE.LOCALFORAGE });
      } catch (error) {
        // 忽略删除不存在数据的错误
      }
    }
    
    console.log('✅ 清理完成');
  }
}

// 导出测试类
export default StorageTest;

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  const test = new StorageTest();
  
  // 添加到全局对象，方便在控制台中使用
  window.storageTest = test;
  
  console.log('💡 使用方法:');
  console.log('  storageTest.runAllTests() - 运行所有测试');
  console.log('  storageTest.cleanup() - 清理测试数据');
}
