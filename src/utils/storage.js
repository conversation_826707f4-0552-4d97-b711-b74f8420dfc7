import Cookies from "js-cookie";
import crypto from "./crypto.js";
import localForage from "./localforage/index.js";
import { validatejson } from "./validate.js";

/**
 * 存储类型枚举
 */
export const STORAGE_TYPE = {
  LOCALSTORAGE: "localStorage",
  SESSION: "sessionStorage",
  COOKIE: "cookie",
  LOCALFORAGE: "localForage",
};

/**
 * 统一存储管理类
 * 支持多种存储方式、过期时间、数据加密
 */
class StorageManager {
  /**
   * 存储数据
   * @param {string} key - 存储键名
   * @param {any} value - 存储值
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型，默认为 localStorage
   * @param {number} options.expire - 过期时间（秒），0表示永不过期
   * @param {boolean} options.encrypt - 是否加密，默认true
   */
  async setItem(key, value, options = {}) {
    const { type = STORAGE_TYPE.LOCALSTORAGE, expire = 0, encrypt = true } = options;
    const _expire = expire * 1000;

    // 构建存储数据对象
    const storageData = {
      value: value,
      timestamp: Date.now(),
      expire: _expire, // 将秒转换为毫秒存储
    };

    // 序列化数据
    let serializedData = JSON.stringify(storageData);

    // 加密处理
    if (encrypt) {
      try {
        serializedData = crypto.encrypt(serializedData);
      } catch (error) {
        console.error("数据加密失败:", error);
        throw new Error("数据加密失败");
      }
    }

    // 根据存储类型进行存储
    try {
      switch (type) {
        case STORAGE_TYPE.LOCALSTORAGE:
          localStorage.setItem(key, serializedData);
          break;

        case STORAGE_TYPE.SESSION:
          sessionStorage.setItem(key, serializedData);
          break;

        case STORAGE_TYPE.COOKIE:
          const cookieOptions = {};
          if (_expire > 0) {
            cookieOptions.expires = new Date(Date.now() + _expire); // 将秒转换为毫秒
          }
          Cookies.set(key, serializedData, cookieOptions);
          break;

        case STORAGE_TYPE.LOCALFORAGE:
          await localForage.setItem(key, serializedData);
          break;

        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
    } catch (error) {
      console.error("数据存储失败:", error);
      throw new Error(`数据存储失败: ${error.message}`);
    }
  }

  /**
   * 获取数据
   * @param {string} key - 存储键名
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型，默认为 localStorage
   * @param {boolean} options.encrypt - 是否解密，默认true
   * @returns {any} 存储的数据，如果不存在或已过期返回null
   */
  async getItem(key, options = {}) {
    const { type = STORAGE_TYPE.LOCALSTORAGE, encrypt = true } = options;

    let serializedData = null;

    // 根据存储类型获取数据
    try {
      switch (type) {
        case STORAGE_TYPE.LOCALSTORAGE:
          serializedData = localStorage.getItem(key);
          break;

        case STORAGE_TYPE.SESSION:
          serializedData = sessionStorage.getItem(key);
          break;

        case STORAGE_TYPE.COOKIE:
          serializedData = Cookies.get(key);
          break;

        case STORAGE_TYPE.LOCALFORAGE:
          serializedData = await localForage.getItem(key);
          break;

        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
    } catch (error) {
      console.error("数据获取失败:", error);
      return null;
    }

    // 如果数据不存在
    if (!serializedData) {
      return null;
    }

    // 解密处理
    if (encrypt) {
      try {
        serializedData = crypto.decrypt(serializedData);
      } catch (error) {
        console.error("数据解密失败:", error);
        // 解密失败时删除损坏的数据
        await this.removeItem(key, { type });
        return null;
      }
    }
    // 反序列化数据
    if (!validatejson(serializedData)) {
      return null;
    }
    const storageData = JSON.parse(serializedData);
    // 检查数据格式
    if (!storageData || typeof storageData !== "object" || !storageData.hasOwnProperty("value")) {
      // 可能是旧格式数据或直接存储的基本类型，直接返回
      return storageData;
    }

    // 检查是否过期
    const { value, timestamp, expire } = storageData;
    if (expire > 0 && Date.now() - timestamp > expire) {
      // 数据已过期，删除并返回null
      await this.removeItem(key, { type });
      return null;
    }

    return value;
  }

  /**
   * 删除数据
   * @param {string} key - 存储键名
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型，默认为 localStorage
   */
  async removeItem(key, options = {}) {
    const { type = STORAGE_TYPE.LOCALSTORAGE } = options;

    try {
      switch (type) {
        case STORAGE_TYPE.LOCALSTORAGE:
          localStorage.removeItem(key);
          break;

        case STORAGE_TYPE.SESSION:
          sessionStorage.removeItem(key);
          break;

        case STORAGE_TYPE.COOKIE:
          Cookies.remove(key);
          break;

        case STORAGE_TYPE.LOCALFORAGE:
          await localForage.removeItem(key);
          break;

        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
    } catch (error) {
      console.error("数据删除失败:", error);
      throw new Error(`数据删除失败: ${error.message}`);
    }
  }

  /**
   * 清空指定类型的所有数据
   * @param {string} type - 存储类型
   */
  async clear(type = STORAGE_TYPE.LOCALSTORAGE) {
    try {
      switch (type) {
        case STORAGE_TYPE.LOCALSTORAGE:
          localStorage.clear();
          break;

        case STORAGE_TYPE.SESSION:
          sessionStorage.clear();
          break;

        case STORAGE_TYPE.COOKIE:
          // Cookie没有直接清空的方法，需要逐个删除
          const cookies = document.cookie.split(";");
          for (let cookie of cookies) {
            const eqPos = cookie.indexOf("=");
            const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
            if (name) {
              Cookies.remove(name);
            }
          }
          break;
        case STORAGE_TYPE.LOCALFORAGE:
          await localForage.clear();
          break;
        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
    } catch (error) {
      console.error("数据清空失败:", error);
      throw new Error(`数据清空失败: ${error.message}`);
    }
  }

  /**
   * 检查指定键是否存在
   * @param {string} key - 存储键名
   * @param {Object} options - 配置选项
   * @param {string} options.type - 存储类型，默认为 localStorage
   * @returns {boolean} 是否存在
   */
  async hasItem(key, options = {}) {
    const data = await this.getItem(key, options);
    return data !== null;
  }

  /**
   * 获取指定存储类型的所有键名
   * @param {string} type - 存储类型
   * @returns {Array} 键名数组
   */
  async getKeys(type = STORAGE_TYPE.LOCALSTORAGE) {
    try {
      switch (type) {
        case STORAGE_TYPE.LOCALSTORAGE:
          return Object.keys(localStorage);

        case STORAGE_TYPE.SESSION:
          return Object.keys(sessionStorage);

        case STORAGE_TYPE.COOKIE:
          const cookies = document.cookie.split(";");
          return cookies
            .map((cookie) => {
              const eqPos = cookie.indexOf("=");
              return eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
            })
            .filter((name) => name);

        case STORAGE_TYPE.LOCALFORAGE:
          return await localForage.db.keys();

        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
    } catch (error) {
      console.error("获取键名失败:", error);
      return [];
    }
  }
}

// 创建单例实例
const storageManager = new StorageManager();

// 导出单例实例和存储类型枚举
export default storageManager;
