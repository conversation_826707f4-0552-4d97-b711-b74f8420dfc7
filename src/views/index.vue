<template>
  <div>
    <van-button type="primary" @click="add">存入</van-button>
    <van-button type="primary" @click="get">获取</van-button>
    <van-button type="primary" @click="clear">清空</van-button>
  </div>
</template>

<script setup>
import Store, { STORAGE_TYPE } from "@/utils/storage";
defineOptions({
  name: "Index",
});
definePage({
  name: "Index",
  meta: {
    layout: "index",
    title: "页面标题",
    navBar: true,
    isAuth: false,
  },
});
let i = 1;
const add = async () => {
  const a = await Store.setItem(`test${i}`, `test${i}`, {
    type: STORAGE_TYPE.COOKIE,
    expire: 10000000,
  });
  i++;
  console.log(a);
};
const get = async () => {
  const a = await Store.getItem("test", {
    type: STORAGE_TYPE.COOKIE,
  });
  console.log(a);
};
const clear = async () => {
  await Store.clear(STORAGE_TYPE.COOKIE);
};
</script>

<style lang="less" scoped></style>
